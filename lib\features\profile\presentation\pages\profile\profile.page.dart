import 'package:flutter/material.dart';
import 'package:Kairos/features/authentication/presentation/pages/accueil/accueil.page.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/features/profile/data/models/user_profile.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final UserProfile userProfile = const UserProfile(
    fullName: "Papa Amadou Diallo",
    profileType: "ETUDIANT",
    userId: "USER12345",
    currentSchool: "SENSOFT - BEM",
    phoneNumber: "+221774294171",
    avatarPath: 'assets/images/img_user.png',
  );

  bool _isLoading = true;
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  void _editProfileImage() {
    debugPrint('Edit profile image button tapped');
  }

  void _goToListSchools(){
    debugPrint("User wants to his list of schools");
    Navigator.pushNamed(context, "/liste_etablissement");
  }

  void _logout() {
    debugPrint('Logout button tapped');
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => AccueilPage()),
    );
  }

  @override
  Widget build(BuildContext context) {
    const double heroHeight = 200.0;
    const double avatarRadius = 100.0;
    const double avatarTopPosition = (heroHeight - avatarRadius)/2 ;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text('Profile'),
        centerTitle: true,
        foregroundColor: Colors.white,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Hero(
                      tag: "hero_profile",
                      transitionOnUserGestures: true,
                      child: Image.asset("assets/images/header_dashboard.png", width: MediaQuery.of(context).size.width, fit: BoxFit.cover,),
                    ),
                    Positioned(
                      top: avatarTopPosition,
                      left: MediaQuery.of(context).size.width / 2 - avatarRadius,
                      child: CircleAvatar(
                        radius: avatarRadius,
                        backgroundImage: AssetImage('assets/images/default_profile_image.jpg'),
                      ),
                    ),

                    Positioned(
                      top: avatarTopPosition+30,
                      left: MediaQuery.of(context).size.width / 2 + avatarRadius - 30,
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 4.0,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.camera_alt),
                          color: Colors.blue,
                          onPressed: _editProfileImage,
                        ),
                      ),
                    ),
                  ],
                ),
               Padding(
                  padding: const EdgeInsets.only(top: avatarRadius, left: 16, right: 16),
                  child:  _isLoading
        ? Center(
          heightFactor: 4,
            child: CustomSpinner(
              size: 60.0,
              strokeWidth: 5.0,
            ),
          )
        : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ListTile(
                              title: Text('Nom complet', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor)),
                              subtitle: Text(userProfile.fullName),
                              contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
                              dense: true,
                            ),
                            Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
                            ListTile(
                              title: Text('Type de profil', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor)),
                              subtitle: Text(userProfile.profileType),
                              contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
                              dense: true,
                            ),
                            Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
                            ListTile(
                              title: Text('Identifiant', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor)),
                              subtitle: Text(userProfile.userId),
                              contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
                              dense: true,
                            ),
                            Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
                            ListTile(
                              title: Text('Établissement', style: TextStyle(fontSize: 8,  color: Theme.of(context).primaryColor)),
                              subtitle: Text(userProfile.currentSchool),
                              contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
                              dense: true,
                            ),
                            Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
                            ListTile(
                              title: Text('Numéro de téléphone', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor),),
                              subtitle: Text(userProfile.phoneNumber),
                              contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
                              dense: true,
                            ),
                            Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
                            const SizedBox(height: 40.0),
                            Center(child:
                            FilledButton(onPressed: _goToListSchools,
                            style: ButtonStyle(
                              minimumSize: WidgetStateProperty.all(const Size(200, 50)),
                              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                            ),
                            child: const Text("VOIR LISTE ÉTABLISSEMENT")),
                            ),
                            const SizedBox(height: 7.0),
                            Center(
                              child: FilledButton(
                                onPressed: _logout,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                  minimumSize: const Size(200, 50),
                                ),
                                child: const Text(
                                  'DÉCONNEXION',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: 20.0),
                          ],
                        ),
                    ),
              ],
            ),
      ),
    );
  }
}
